import React, { useState, useEffect } from 'react';
import { apiGet } from '../utils/apiUtils';
import './DownloadConfigForm.css';

const DownloadConfigForm = ({ users, onSubmit, loading, initialData }) => {
    const [formData, setFormData] = useState({
        name: '',
        selectedUsers: [],
        downloadPath: '',
        concurrentDownloads: 3,
        maxRetries: 3,
        stopOnError: true,
        continueOnError: false,
        skipMimeTypes: '',
        processingOrder: 'created_at'
    });
    const [selectAll, setSelectAll] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [errors, setErrors] = useState({});
    const [userFileStats, setUserFileStats] = useState({});

    useEffect(() => {
        if (initialData) {
            setFormData({
                name: initialData.name || '',
                selectedUsers: initialData.selected_users || [],
                downloadPath: initialData.download_path || '',
                concurrentDownloads: initialData.concurrent_downloads || 3,
                maxRetries: initialData.max_retries || 3,
                stopOnError: initialData.stop_on_error !== undefined ? initialData.stop_on_error : true,
                continueOnError: initialData.continue_on_error !== undefined ? initialData.continue_on_error : false,
                skipMimeTypes: (initialData.skip_mime_types || []).join(', '),
                processingOrder: initialData.processing_order || 'created_at'
            });
        }
    }, [initialData]);

    // Filter users based on search term
    const filteredUsers = users.filter(user => {
        const searchLower = searchTerm.toLowerCase();
        return (
            user.primary_email.toLowerCase().includes(searchLower) ||
            (user.full_name && user.full_name.toLowerCase().includes(searchLower))
        );
    });

    // Handle form input changes
    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));

        // Clear error for this field
        if (errors[name]) {
            setErrors(prev => ({
                ...prev,
                [name]: null
            }));
        }
    };

    // Handle error handling option changes
    const handleErrorHandlingChange = (e) => {
        const { name, checked } = e.target;

        if (name === 'stopOnError') {
            setFormData(prev => ({
                ...prev,
                stopOnError: checked,
                continueOnError: !checked
            }));
        } else if (name === 'continueOnError') {
            setFormData(prev => ({
                ...prev,
                continueOnError: checked,
                stopOnError: !checked
            }));
        }
    };

    // Handle user selection
    const handleUserSelect = (email) => {
        setFormData(prev => {
            const isSelected = prev.selectedUsers.includes(email);
            let newSelectedUsers;

            if (isSelected) {
                newSelectedUsers = prev.selectedUsers.filter(e => e !== email);
            } else {
                newSelectedUsers = [...prev.selectedUsers, email];
            }

            // Update selectAll state
            if (newSelectedUsers.length === users.length) {
                setSelectAll(true);
            } else {
                setSelectAll(false);
            }

            return {
                ...prev,
                selectedUsers: newSelectedUsers
            };
        });

        // Clear error for selectedUsers
        if (errors.selectedUsers) {
            setErrors(prev => ({
                ...prev,
                selectedUsers: null
            }));
        }
    };

    // Handle select all users
    const handleSelectAll = () => {
        const newSelectAll = !selectAll;
        setSelectAll(newSelectAll);

        if (newSelectAll) {
            setFormData(prev => ({
                ...prev,
                selectedUsers: users.map(user => user.primary_email)
            }));
        } else {
            setFormData(prev => ({
                ...prev,
                selectedUsers: []
            }));
        }

        // Clear error for selectedUsers
        if (errors.selectedUsers) {
            setErrors(prev => ({
                ...prev,
                selectedUsers: null
            }));
        }
    };

    // Handle select all undownloaded files for a user
    const handleSelectAllUndownloadedFiles = async (userEmail) => {
        try {
            const response = await apiGet(`/api/download/users/${encodeURIComponent(userEmail)}/undownloaded-files`);

            if (response.success) {
                const undownloadedCount = response.data.length;
                setUserFileStats(prev => ({
                    ...prev,
                    [userEmail]: {
                        ...prev[userEmail],
                        undownloadedCount
                    }
                }));

                // Auto-select this user if they have undownloaded files
                if (undownloadedCount > 0 && !formData.selectedUsers.includes(userEmail)) {
                    handleUserSelect(userEmail);
                }
            }
        } catch (error) {
            console.error('Error getting undownloaded files:', error);
        }
    };

    // Handle form submission
    const handleSubmit = (e) => {
        e.preventDefault();

        // Validate form
        const newErrors = {};

        if (!formData.name.trim()) {
            newErrors.name = 'Session name is required';
        }

        if (formData.selectedUsers.length === 0) {
            newErrors.selectedUsers = 'Please select at least one user';
        }

        if (!formData.downloadPath.trim()) {
            newErrors.downloadPath = 'Download path is required';
        }

        if (Object.keys(newErrors).length > 0) {
            setErrors(newErrors);
            return;
        }

        // Process skipMimeTypes string to array
        const skipMimeTypesArray = formData.skipMimeTypes
            .split(',')
            .map(type => type.trim())
            .filter(type => type.length > 0);

        // Submit form with processed data
        onSubmit({
            ...formData,
            skipMimeTypes: skipMimeTypesArray
        });
    };

    // Format file size
    const formatFileSize = (bytes) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    return (
        <div className="download-config-form">
            <h2>Configure Download Session</h2>

            <form onSubmit={handleSubmit}>
                {/* Session Name */}
                <div className="form-group">
                    <label htmlFor="name">Session Name</label>
                    <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleChange}
                        placeholder="Enter a name for this download session"
                        disabled={loading}
                    />
                    {errors.name && <div className="error-message">{errors.name}</div>}
                </div>

                {/* Download Path */}
                <div className="form-group">
                    <label htmlFor="downloadPath">Download Path</label>
                    <input
                        type="text"
                        id="downloadPath"
                        name="downloadPath"
                        value={formData.downloadPath}
                        onChange={handleChange}
                        placeholder="Enter local path to save files (e.g., /Users/<USER>/Downloads)"
                        disabled={loading}
                    />
                    {errors.downloadPath && <div className="error-message">{errors.downloadPath}</div>}
                    <div className="help-text">
                        Files will be saved in this directory, organized by user email
                    </div>
                </div>

                {/* Concurrent Downloads */}
                <div className="form-row">
                    <div className="form-group">
                        <label htmlFor="concurrentDownloads">Concurrent Downloads</label>
                        <input
                            type="number"
                            id="concurrentDownloads"
                            name="concurrentDownloads"
                            value={formData.concurrentDownloads}
                            onChange={handleChange}
                            min="1"
                            max="10"
                            disabled={loading}
                        />
                        <div className="help-text">
                            Number of files to download simultaneously
                        </div>
                    </div>

                    {/* Max Retries */}
                    <div className="form-group">
                        <label htmlFor="maxRetries">Max Retries</label>
                        <input
                            type="number"
                            id="maxRetries"
                            name="maxRetries"
                            value={formData.maxRetries}
                            onChange={handleChange}
                            min="0"
                            max="10"
                            disabled={loading}
                        />
                        <div className="help-text">
                            Number of times to retry failed downloads
                        </div>
                    </div>
                </div>

                {/* Error Handling Options */}
                <div className="form-group">
                    <label>Error Handling</label>
                    <div className="error-handling-options">
                        <div className="radio-option">
                            <label>
                                <input
                                    type="radio"
                                    name="stopOnError"
                                    checked={formData.stopOnError}
                                    onChange={handleErrorHandlingChange}
                                    disabled={loading}
                                />
                                <div className="option-content">
                                    <div className="option-title">Stop on Error (Default)</div>
                                    <div className="option-description">
                                        Stop the entire download process if any error occurs and log the error
                                    </div>
                                </div>
                            </label>
                        </div>

                        <div className="radio-option">
                            <label>
                                <input
                                    type="radio"
                                    name="continueOnError"
                                    checked={formData.continueOnError}
                                    onChange={handleErrorHandlingChange}
                                    disabled={loading}
                                />
                                <div className="option-content">
                                    <div className="option-title">Continue on Error</div>
                                    <div className="option-description">
                                        Continue downloading other files if an error occurs and log errors for later review
                                    </div>
                                </div>
                            </label>
                        </div>
                    </div>
                </div>

                {/* Skip MIME Types */}
                <div className="form-group">
                    <label htmlFor="skipMimeTypes">Skip MIME Types (Optional)</label>
                    <input
                        type="text"
                        id="skipMimeTypes"
                        name="skipMimeTypes"
                        value={formData.skipMimeTypes}
                        onChange={handleChange}
                        placeholder="e.g., application/vnd.google-apps.shortcut, application/vnd.google-apps.folder"
                        disabled={loading}
                    />
                    <div className="help-text">
                        Comma-separated list of MIME types to skip during migration. Common examples:
                        <br />• application/vnd.google-apps.shortcut (shortcuts)
                        <br />• application/vnd.google-apps.folder (folders)
                    </div>
                </div>

                {/* Processing Order */}
                <div className="form-group">
                    <label htmlFor="processingOrder">Processing Order</label>
                    <select
                        id="processingOrder"
                        name="processingOrder"
                        value={formData.processingOrder}
                        onChange={handleChange}
                        disabled={loading}
                    >
                        <option value="created_at">Default (Creation Time)</option>
                        <option value="user_email">By User Email (Process all files for one user first)</option>
                        <option value="size_asc">By File Size (Smallest First)</option>
                        <option value="size_desc">By File Size (Largest First)</option>
                    </select>
                    <div className="help-text">
                        Choose the order in which files will be processed during download
                    </div>
                </div>

                {/* User Selection */}
                <div className="form-group">
                    <label>Select Users to Download</label>
                    {errors.selectedUsers && <div className="error-message">{errors.selectedUsers}</div>}

                    <div className="user-selection-header">
                        <div className="search-box">
                            <input
                                type="text"
                                placeholder="Search users..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                disabled={loading}
                            />
                        </div>

                        <div className="select-all">
                            <label>
                                <input
                                    type="checkbox"
                                    checked={selectAll}
                                    onChange={handleSelectAll}
                                    disabled={loading}
                                />
                                Select All Users
                            </label>
                        </div>
                    </div>

                    <div className="users-list">
                        {filteredUsers.length === 0 ? (
                            <div className="no-users">No users found</div>
                        ) : (
                            filteredUsers.map(user => (
                                <div key={user.user_id} className="user-item">
                                    <label className="user-checkbox">
                                        <input
                                            type="checkbox"
                                            checked={formData.selectedUsers.includes(user.primary_email)}
                                            onChange={() => handleUserSelect(user.primary_email)}
                                            disabled={loading}
                                        />
                                        <div className="user-info-single-line">
                                            <span className="user-name">
                                                {user.full_name || user.primary_email}
                                            </span>
                                            <span className="user-email">
                                                ({user.primary_email})
                                            </span>
                                            <span className="user-stats-inline">
                                                • {user.fileCount} files • {formatFileSize(user.totalSize)}
                                            </span>
                                        </div>
                                    </label>

                                    {/* Select All Undownloaded Files Button */}
                                    <button
                                        type="button"
                                        className="select-undownloaded-btn"
                                        onClick={() => handleSelectAllUndownloadedFiles(user.primary_email)}
                                        disabled={loading}
                                        title="Select all undownloaded files for this user"
                                    >
                                        📥 Select Undownloaded
                                    </button>
                                </div>
                            ))
                        )}
                    </div>
                </div>

                {/* Submit Button */}
                <div className="form-actions">
                    <button
                        type="submit"
                        className="btn btn-primary"
                        disabled={loading}
                    >
                        {loading ? 'Creating...' : 'Create Download Session'}
                    </button>
                </div>
            </form>
        </div>
    );
};

export default DownloadConfigForm;
